# LocalStack Environment Configuration
# This configuration imports from the core infrastructure and adapts it for LocalStack

module "core" {
  source = "../core"

  env = "local"

  # Network Configuration for LocalStack Environment
  # Simplified networking for local development
  vpc_cidr             = "10.2.0.0/16" # Local: 10.2.0.0/16 (separate from UAT/Prod)
  public_subnet_count  = 2             # 2 public subnets for ALB
  private_subnet_count = 2             # 2 private subnets for ECS tasks

  # NAT Gateway Configuration - Disabled for LocalStack
  enable_nat_gateway   = false    # LocalStack: Disabled for simplicity
  nat_gateway_strategy = "single" # Not used when NAT Gateway is disabled
  use_private_subnets  = false    # LocalStack: Use public subnets for simplicity

  # Domain Configuration for LocalStack
  # Using localhost domains for local testing
  # Local: api.local.ezychat.ai (will resolve to localhost)
  domain_name     = "local.ezychat.ai"
  api_subdomain   = "api"
}

# LocalStack-specific outputs
output "localstack_api_gateway_url" {
  description = "LocalStack API Gateway URL"
  value       = module.core.api_gateway_url
}

output "localstack_lambda_function_names" {
  description = "LocalStack Lambda function names"
  value       = module.core.lambda_function_names
}

output "localstack_s3_bucket_names" {
  description = "LocalStack S3 bucket names"
  value       = module.core.s3_bucket_names
}

output "localstack_ecr_repository_urls" {
  description = "LocalStack ECR repository URLs"
  value       = module.core.ecr_repository_urls
}
